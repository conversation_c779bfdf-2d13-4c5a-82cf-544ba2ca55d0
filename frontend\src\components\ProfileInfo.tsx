// import { User } from '../../../server/prisma/generated/zod/modelSchema'

// import { useUserStore } from '@/store'
import { User as UserType } from '@/types'
import { Avatar, Button, Card, CardBody, CardFooter, CardHeader, Textarea, User } from "@heroui/react"
import { UserRating } from './UserRating'
import { useTranslation } from 'react-i18next'
import { Link } from '@tanstack/react-router'
import IconMessage from '@/lib/svg/MessageIcon'
import { LockIcon } from './LockIcon'
import NotificationsIcon from '@/lib/svg/NotificationIcon'
// import { parsePhoneNumber } from 'react-phone-number-input'

interface Props {
  user?: UserType
  isSelf?: boolean
}

export const ProfileInfo = ({ user, isSelf }: Props) => {
  //   const { data: sessionUserData } = useUserStore()
  const { t } = useTranslation()

  return (
    <Card key={user?.id} shadow='lg'>
      {/* <CardHeader className='flex justify-center'>
        <h1 className='text-xl text-center text-ellipsis font-bold truncate'>{user?.username}</h1>
      </CardHeader> */}
      <CardBody>
        <div className='flex flex-col items-center'>
          <div className='flex items-end flex-wrap'>
            <Avatar name={user?.username} src={user?.avatar?.base64string} size='lg' />
            <div className='ml-5'>
              <h1 className='text-lg text-center text-ellipsis font-bold truncate mt-5'>{user?.username}</h1>
              <UserRating user={user} />
            </div>
            <Textarea label={t('About me')} className='mt-5' readOnly value={user?.about_me ?? ''} />
          </div>
          {/* <div className='inline-block align-bottom bg-default-100 rounded-lg text-left overflow-hidden transform transition-all mb-4 w-full sm:w-1/3 sm:my-8'>
            <div className='bg-default-100 p-5'>
              <div className='sm:flex sm:items-start'>
                <div className='text-center sm:mt-0 sm:ml-2 sm:text-left'>
                  <h3 className='text-sm leading-6 font-medium text-default-400'>{t('Total')}</h3>
                  <p className='text-3xl font-bold'>{user?._count?.cases || 0}</p>
                </div>
              </div>
            </div>
          </div> */}
          <div className='mt-5 flex flex-wrap items-stretch space-x-3 space-y-3'>
            <div></div>
            <div className='flex flex-col space-y-2 items-center rounded-xl bg-default-100 px-4 py-2'>
              <p className='text-sm font-medium text-default-500'>{t('Subscribers')}</p>
              <p className='text-3xl font-medium text-default-600'>{<LockIcon/>}</p>
            </div>
            <div className='flex flex-col space-y-2 items-center rounded-xl bg-default-100 px-4 py-2'>
              <p className='text-sm font-medium text-default-500'>{t('Total')}</p>
              <p className='text-3xl font-medium text-default-600'>{user?._count?.cases || 0}</p>
            </div>
            <div className='flex flex-col space-y-2 items-center rounded-xl bg-default-100 px-4 py-2'>
              <p className='text-sm font-medium text-default-500'>{t('Completed')}</p>
              <p className='text-3xl font-medium text-default-600'>{<LockIcon />}</p>
            </div>
          </div>
        </div>
        {/* <div className='flex flex-col items-center'>
          <div className='mt-4 text-center'>
            <p className='mt-1 text-default-500'>
              <a href={'email:' + user?.email}>{user?.email}</a>
            </p>
          </div>
        </div> */}
        <CardFooter>
          <div className=' w-full mt-10 flex flex-wrap justify-end space-x-3'>
            <Button endContent={<IconMessage className='w-6 h-6' />}>
              <Link to='/messages' search={{ partnerId: user?.id }}>
                {t('Message')}
              </Link>
            </Button>
            <Button disabled endContent={<NotificationsIcon className='w-6 h-6' />}>
              {t('Subscribe')}
            </Button>
          </div>
        </CardFooter>
      </CardBody>
    </Card>
  )
}
