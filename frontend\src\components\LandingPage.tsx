import { motion, useMotionValue, useTransform, useSpring } from 'framer-motion'
import { Button, Card, CardBody, Image, Chip } from '@heroui/react'
import { useTranslation } from 'react-i18next'
import { Link } from '@tanstack/react-router'
import { useRef, type FC, type ReactNode, type MouseEvent } from 'react'
import { cn } from '@/lib/utils'

// Анимационные варианты
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: 'easeOut'
    }
  }
}

const cardVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: 'easeOut'
    }
  },
  hover: {
    scale: 1.05,
    transition: {
      duration: 0.2
    }
  }
}

const floatingVariants = {
  animate: {
    y: [-10, 10, -10],
    transition: {
      duration: 3,
      repeat: Infinity,
      ease: 'easeInOut'
    }
  }
}

// Tilt-обертка для 3D-эффекта карточек
type TiltCardProps = { children: ReactNode; className?: string }
const TiltCard: FC<TiltCardProps> = ({ children, className }) => {
  const ref = useRef<HTMLDivElement>(null)
  const x = useMotionValue(0)
  const y = useMotionValue(0)
  const xSpring = useSpring(x, { stiffness: 300, damping: 40 })
  const ySpring = useSpring(y, { stiffness: 300, damping: 40 })

  const transform = useTransform(
    [xSpring, ySpring],
    ([newX, newY]) => `perspective(1000px) rotateX(${newY}deg) rotateY(${newX}deg) scale3d(1.05, 1.05, 1.05)`
  )

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!ref.current) return

    const rect = ref.current.getBoundingClientRect()

    const width = rect.width
    const height = rect.height

    const mouseX = e.clientX - rect.left
    const mouseY = e.clientY - rect.top

    const xPct = mouseX / width - 0.5
    const yPct = mouseY / height - 0.5

    x.set(xPct * 20) // Intensity reduced from 30 to 20 for a subtler effect
    y.set(yPct * -20)
  }

  const handleMouseLeave = () => {
    x.set(0)
    y.set(0)
  }

  return (
    <motion.div
      ref={ref}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      style={{ transform, transformStyle: 'preserve-3d' }}
      className={cn('relative rounded-xl will-change-transform transform-gpu', className)}
    >
      {children}
    </motion.div>
  )
}

export const LandingPage = () => {
  const { t } = useTranslation()

  // Параллакс для Hero-блока
  const heroRef = useRef<HTMLElement | null>(null)
  const mx = useMotionValue(0)
  const my = useMotionValue(0)
  const parallaxSlow = {
    x: useSpring(useTransform(mx, [-1, 1], [-20, 20]), { stiffness: 60, damping: 20 }),
    y: useSpring(useTransform(my, [-1, 1], [-10, 10]), { stiffness: 60, damping: 20 })
  }
  const parallaxFast = {
    x: useSpring(useTransform(mx, [-1, 1], [-35, 35]), { stiffness: 70, damping: 18 }),
    y: useSpring(useTransform(my, [-1, 1], [-18, 18]), { stiffness: 70, damping: 18 })
  }

  function onHeroMouseMove(e: MouseEvent<HTMLElement>) {
    if (!heroRef.current) return
    const rect = heroRef.current.getBoundingClientRect()
    const px = (e.clientX - rect.left) / rect.width // 0..1
    const py = (e.clientY - rect.top) / rect.height // 0..1
    mx.set(px * 2 - 1) // -1..1
    my.set(py * 2 - 1) // -1..1
  }

  const features = [
    {
      icon: '🌍',
      title: t('Global Network'),
      description: t('Connect with travelers worldwide for fast and reliable delivery')
    },
    {
      icon: '💰',
      title: t('Cost Effective'),
      description: t('Save up to 70% compared to traditional shipping services')
    },
    {
      icon: '⚡',
      title: t('Fast Delivery'),
      description: t('Get your packages delivered faster through our traveler network')
    },
    {
      icon: '🔒',
      title: t('Secure & Safe'),
      description: t('Verified users, ratings system, and secure payment protection')
    },
    {
      icon: '📱',
      title: t('Real-time Tracking'),
      description: t('Track your package in real-time with live chat communication')
    },
    {
      icon: '🌟',
      title: t('Community Driven'),
      description: t('Join a community of trusted travelers and senders worldwide')
    }
  ]

  const steps = [
    {
      number: '01',
      title: t('Create Request'),
      description: t('Post your delivery request with pickup and destination details')
    },
    {
      number: '02',
      title: t('Find Traveler'),
      description: t('Browse available travelers or wait for offers from our community')
    },
    {
      number: '03',
      title: t('Secure Deal'),
      description: t('Agree on terms, make secure payment, and exchange contact details')
    },
    {
      number: '04',
      title: t('Track & Receive'),
      description: t('Track your package in real-time and receive it at destination')
    }
  ]

  // Текстовый SVG-noise (без бинарников)
  const NOISE_SVG = '%3Csvg xmlns="http://www.w3.org/2000/svg" width="200" height="200"%3E%3Cfilter id="n"%3E%3CfeTurbulence baseFrequency=".8" numOctaves="2" stitchTiles="stitch"/%3E%3C/filter%3E%3Crect width="100%25" height="100%25" filter="url(%23n)" opacity=".05"/%3E%3C/svg%3E'

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
      className="w-full"
    >
      {/* Hero Section */}
      <motion.section
        variants={itemVariants}
        className="relative isolate py-20 lg:py-32 overflow-hidden"
        ref={heroRef as unknown as React.Ref<HTMLElement>}
        onMouseMove={onHeroMouseMove}
      >
        {/* Aurora background */}
        <div className="absolute inset-0 pointer-events-none" aria-hidden>
          <div className="absolute inset-0 bg-gradient-to-br from-primary-50 via-secondary-50 to-success-50 dark:from-primary-950 dark:via-secondary-950 dark:to-success-950" />
          <div className="absolute inset-0 [mask-image:radial-gradient(ellipse_at_center,black,transparent_70%)]">
            <motion.div
              aria-hidden
              className="absolute -top-24 -left-24 w-[60vmax] h-[60vmax] rounded-full blur-3xl bg-gradient-to-br from-primary-400/35 to-secondary-400/35"
              animate={{ x: ['-5%', '5%', '-5%'], y: ['-5%', '5%', '-5%'] }}
              transition={{ duration: 20, repeat: Infinity, ease: 'easeInOut' }}
              style={{ x: parallaxSlow.x, y: parallaxSlow.y }}
            />
            <motion.div
              aria-hidden
              className="absolute -bottom-24 -right-24 w-[55vmax] h-[55vmax] rounded-full blur-3xl bg-gradient-to-br from-success-400/30 to-primary-400/30"
              animate={{ x: ['6%', '-6%', '6%'], y: ['-4%', '4%', '-4%'] }}
              transition={{ duration: 22, repeat: Infinity, ease: 'easeInOut' }}
              style={{ x: parallaxFast.x, y: parallaxFast.y }}
            />
            <motion.div
              aria-hidden
              className="absolute top-1/3 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[40vmax] h-[40vmax] rounded-full blur-3xl bg-gradient-to-br from-secondary-400/25 to-success-400/25"
              animate={{ scale: [1, 1.08, 1] }}
              transition={{ duration: 18, repeat: Infinity, ease: 'easeInOut' }}
            />
          </div>

          {/* Subtle grid */}
          <div
            className="absolute inset-0 opacity-[0.25] dark:opacity-[0.18] mix-blend-overlay"
            style={{
              backgroundImage:
                'radial-gradient(rgba(120,120,120,0.18) 1px, transparent 1px)',
              backgroundSize: '24px 24px',
              backgroundPosition: '0 0'
            }}
          />

          {/* Noise overlay */}
          <div
            className="absolute inset-0 opacity-[0.08]"
            style={{
              backgroundImage: `url("data:image/svg+xml,${NOISE_SVG}")`
            }}
          />
        </div>

        {/* Floating elements (с параллаксом) */}
        <motion.div
          variants={floatingVariants}
          animate="animate"
          className="absolute top-20 left-10 w-20 h-20 bg-primary-200 dark:bg-primary-800 rounded-full opacity-20 pointer-events-none"
          style={{ x: parallaxSlow.x, y: parallaxSlow.y }}
        />
        <motion.div
          variants={floatingVariants}
          animate="animate"
          transition={{ duration: 3, repeat: Infinity, ease: 'easeInOut', delay: 1 }}
          className="absolute top-40 right-20 w-16 h-16 bg-secondary-200 dark:bg-secondary-800 rounded-full opacity-20 pointer-events-none"
        />
        <motion.div
          variants={floatingVariants}
          animate="animate"
          transition={{ duration: 3, repeat: Infinity, ease: 'easeInOut', delay: 2 }}
          className="absolute bottom-20 left-1/4 w-12 h-12 bg-success-200 dark:bg-success-800 rounded-full opacity-20 pointer-events-none"
          style={{ x: parallaxFast.x, y: parallaxFast.y }}
        />

        <div className="relative container mx-auto px-4 text-center">
          <motion.div variants={itemVariants}>
            <Chip color="primary" variant="flat" className="mb-6">
              🚀 {t('Revolutionary Crowdshipping Platform')}
            </Chip>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-primary-600 via-secondary-600 to-success-600 bg-clip-text text-transparent"
            style={{ x: parallaxSlow.x, y: parallaxSlow.y }}
          >
            {t('Deliver Anywhere,')}
            <br />
            {t('Travel Everywhere')}
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl md:text-2xl text-default-600 mb-8 max-w-3xl mx-auto leading-relaxed"
          >
            {t('Connect with travelers to send packages worldwide. Fast, affordable, and secure crowdshipping platform that revolutionizes global delivery.')}
          </motion.p>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Button
              as={Link}
              to="/create"
              size="lg"
              color="primary"
              className="px-8 py-6 text-lg font-semibold"
            >
              {t('Send Package')} 📦
            </Button>
            <Button
              as={Link}
              to="/"
              size="lg"
              variant="bordered"
              className="px-8 py-6 text-lg font-semibold"
            >
              {t('Become Traveler')} ✈️
            </Button>
          </motion.div>
        </div>
      </motion.section>

      {/* Features Section */}
      <motion.section variants={itemVariants} className="py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-5xl font-bold mb-6 text-default-800">
              {t('Why Choose TakeNPass?')}
            </h2>
            <p className="text-xl text-default-600 max-w-2xl mx-auto">
              {t('Experience the future of package delivery with our innovative crowdshipping platform')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div key={index} variants={cardVariants} className="h-full">
                <TiltCard>
                  <Card className="h-full hover:shadow-xl transition-shadow duration-300 transform-gpu will-change-transform">
                    <CardBody className="p-8 text-center">
                      <div className="text-4xl mb-4">{feature.icon}</div>
                      <h3 className="text-xl font-bold mb-3 text-default-800">{feature.title}</h3>
                      <p className="text-default-600 leading-relaxed">{feature.description}</p>
                    </CardBody>
                  </Card>
                </TiltCard>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* How it Works Section */}
      <motion.section variants={itemVariants} className="py-20 lg:py-32 bg-default-50 dark:bg-default-950">
        <div className="container mx-auto px-4">
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-5xl font-bold mb-6 text-default-800">{t('How It Works')}</h2>
            <p className="text-xl text-default-600 max-w-2xl mx-auto">{t('Simple steps to send your package anywhere in the world')}</p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {steps.map((step, index) => (
              <motion.div key={index} variants={cardVariants} className="text-center">
                <div className="relative mb-6">
                  <div className="w-20 h-20 mx-auto bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                    {step.number}
                  </div>
                  {index < steps.length - 1 && (
                    <div className="hidden lg:block absolute top-10 left-full w-full h-0.5 bg-gradient-to-r from-primary-300 to-secondary-300" />
                  )}
                </div>
                <h3 className="text-xl font-bold mb-3 text-default-800">{step.title}</h3>
                <p className="text-default-600 leading-relaxed">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Benefits Section */}
      <motion.section variants={itemVariants} className="py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <motion.div variants={itemVariants}>
              <h2 className="text-3xl md:text-5xl font-bold mb-6 text-default-800">{t('Join the Revolution')}</h2>
              <p className="text-xl text-default-600 mb-8 leading-relaxed">
                {t('TakeNPass is transforming how packages are delivered worldwide. Join thousands of users who trust our platform for their shipping needs.')}
              </p>

              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-full flex items-center justify-center">
                    <span className="text-success-600 text-xl">✓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-default-800">{t('Verified Community')}</h4>
                    <p className="text-default-600">{t('All users are verified with ratings and reviews')}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-full flex items-center justify-center">
                    <span className="text-success-600 text-xl">✓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-default-800">{t('Global Coverage')}</h4>
                    <p className="text-default-600">{t('Deliver to any destination worldwide')}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-full flex items-center justify-center">
                    <span className="text-success-600 text-xl">✓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-default-800">{t('24/7 Support')}</h4>
                    <p className="text-default-600">{t('Round-the-clock customer support')}</p>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div variants={itemVariants} className="relative">
              <div className="relative z-10">
                <Image src="/heroblock_12.jpg" alt="TakeNPass Platform" className="rounded-2xl shadow-2xl" />
              </div>
              <div className="absolute inset-0 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-2xl transform rotate-3" />
              <div className="absolute inset-0 bg-gradient-to-br from-secondary-500/20 to-success-500/20 rounded-2xl transform -rotate-3" />
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* CTA Section */}
      <motion.section
        variants={itemVariants}
        className="py-20 lg:py-32 bg-gradient-to-br from-primary-600 via-secondary-600 to-success-600 text-white relative isolate overflow-hidden"
      >
        <div className="absolute inset-0 bg-black/20" />
        <motion.div
          variants={floatingVariants}
          animate="animate"
          className="absolute top-10 right-10 w-32 h-32 bg-white/10 rounded-full pointer-events-none"
        />
        <motion.div
          variants={floatingVariants}
          animate="animate"
          style={{ animationDelay: '1.5s' }}
          className="absolute bottom-10 left-10 w-24 h-24 bg-white/10 rounded-full pointer-events-none"
        />

        <div className="relative container mx-auto px-4 text-center">
          <motion.h2 variants={itemVariants} className="text-3xl md:text-5xl font-bold mb-6">
            {t('Ready to Get Started?')}
          </motion.h2>
          <motion.p variants={itemVariants} className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            {t('Join thousands of users who trust TakeNPass for their global shipping needs')}
          </motion.p>
          <motion.div variants={itemVariants} className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button as={Link} to="/reg" size="lg" className="px-8 py-6 text-lg font-semibold bg-white text-primary-600 hover:bg-white/90">
              {t('Sign Up Now')} 🚀
            </Button>
            <Button as={Link} to="/" size="lg" variant="bordered" className="px-8 py-6 text-lg font-semibold border-white text-white hover:bg-white/10">
              {t('Learn More')} 📖
            </Button>
          </motion.div>
        </div>
      </motion.section>
    </motion.div>
  )
}